import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { message } from 'antd';

interface RouteGuardOptions {
  requiresAuth?: boolean;
  allowedRoles?: string[];
  redirectTo?: string;
  guestOnly?: boolean;
}

/**
 * 高阶组件方式的路由守卫
 * 类似Vue的mixins概念
 */
export const withRouteGuard = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: RouteGuardOptions = {}
) => {
  const GuardedComponent: React.FC<P> = props => {
    const location = useLocation();
    const { isAuthenticated, user } = useAuthStore();

    const {
      requiresAuth = false,
      allowedRoles = [],
      redirectTo = '/',
      guestOnly = false,
    } = options;

    // 游客专用页面检查
    if (guestOnly && isAuthenticated) {
      return <Navigate to={redirectTo} replace />;
    }

    // 需要登录的页面检查
    if (requiresAuth && !isAuthenticated) {
      message.warning('请先登录');
      return <Navigate to="/login" state={{ from: location }} replace />;
    }

    // 角色权限检查
    if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
      message.error('您没有权限访问此页面');
      return <Navigate to={redirectTo} replace />;
    }

    return <WrappedComponent {...props} />;
  };

  GuardedComponent.displayName = `withRouteGuard(${WrappedComponent.displayName || WrappedComponent.name})`;

  return GuardedComponent;
};
