# EmailCodeInput 组件

一个带有倒计时功能的邮箱验证码输入组件，可以与 Ant Design Form 完美集成。

## 功能特性

- ✅ 支持 Ant Design Form 表单集成
- ✅ 自动倒计时功能（默认60秒）
- ✅ 发送验证码状态管理
- ✅ 自定义发送验证码回调
- ✅ 完全可定制的样式
- ✅ TypeScript 支持
- ✅ 自动清理定时器

## Props 接口

```typescript
interface EmailCodeInputProps {
  value?: string;                    // 当前输入值（由 Form.Item 自动传递）
  onChange?: (value: string) => void; // 值变化回调（由 Form.Item 自动传递）
  placeholder?: string;              // 输入框占位符
  onSendCode?: () => Promise<void> | void; // 发送验证码回调
  disabled?: boolean;                // 是否禁用
  countdownTime?: number;           // 倒计时时间（秒），默认60
  className?: string;               // 自定义样式类名
  size?: 'small' | 'middle' | 'large'; // 输入框大小
}
```

## 基本用法

### 在 Form 中使用

```tsx
import React from 'react';
import { Form, Button } from 'antd';
import EmailCodeInput from '@/components/EmailCodeInput';

const MyForm = () => {
  const [form] = Form.useForm();

  const handleSendCode = async () => {
    try {
      // 调用发送验证码 API
      await api.sendEmailCode({ email: '<EMAIL>' });
      console.log('验证码发送成功');
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error; // 重新抛出错误，让组件处理
    }
  };

  const handleSubmit = (values: any) => {
    console.log('表单值:', values);
  };

  return (
    <Form form={form} onFinish={handleSubmit}>
      <Form.Item
        name="emailCode"
        label="邮箱验证码"
        rules={[{ required: true, message: '请输入邮箱验证码' }]}
      >
        <EmailCodeInput 
          onSendCode={handleSendCode}
          placeholder="请输入6位验证码"
          countdownTime={60}
        />
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit">
          提交
        </Button>
      </Form.Item>
    </Form>
  );
};
```

### 独立使用（不在 Form 中）

```tsx
import React, { useState } from 'react';
import EmailCodeInput from '@/components/EmailCodeInput';

const StandaloneExample = () => {
  const [code, setCode] = useState('');

  const handleSendCode = async () => {
    // 发送验证码逻辑
    console.log('发送验证码');
  };

  return (
    <EmailCodeInput
      value={code}
      onChange={setCode}
      onSendCode={handleSendCode}
      placeholder="请输入验证码"
    />
  );
};
```

## 高级用法

### 自定义倒计时时间

```tsx
<EmailCodeInput
  onSendCode={handleSendCode}
  countdownTime={120} // 2分钟倒计时
/>
```

### 自定义样式

```tsx
<EmailCodeInput
  onSendCode={handleSendCode}
  className="my-custom-input"
  size="small"
/>
```

### 禁用状态

```tsx
<EmailCodeInput
  onSendCode={handleSendCode}
  disabled={true}
/>
```

## 注意事项

1. **表单集成**: 当在 `Form.Item` 中使用时，`value` 和 `onChange` 会由 Ant Design 自动传递，无需手动设置。

2. **错误处理**: `onSendCode` 回调如果抛出错误，组件会自动处理并停止倒计时。

3. **内存泄漏**: 组件会自动清理定时器，无需担心内存泄漏问题。

4. **倒计时状态**: 倒计时期间按钮会自动禁用，倒计时结束后自动恢复。

## 问题解决

### 为什么 Form 获取不到值？

确保组件正确接收和处理了 `value` 和 `onChange` props：

```tsx
// ❌ 错误：没有处理表单 props
const BadComponent = () => {
  return <Input />;
};

// ✅ 正确：处理表单 props
const GoodComponent = ({ value, onChange }) => {
  return <Input value={value} onChange={(e) => onChange?.(e.target.value)} />;
};
```

这就是为什么原来的 `SendCodeInput` 组件无法与表单集成的原因。
