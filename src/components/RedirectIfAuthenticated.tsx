import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';

interface RedirectIfAuthenticatedProps {
  children: React.ReactNode;
  redirectTo?: string; // 重定向目标，默认为首页
}

/**
 * 认证重定向守卫组件
 * 如果用户已登录，则重定向到指定页面，默认为`/`。
 * 如果用户未登录，则渲染子组件。
 *
 * 主要用于保护登录/注册页面，防止已登录用户访问。
 */
const RedirectIfAuthenticated: React.FC<RedirectIfAuthenticatedProps> = ({
  children,
  redirectTo = '/',
}) => {
  const location = useLocation();
  const { isAuthenticated } = useAuthStore();

  if (isAuthenticated) {
    const redirectTarget = location.state?.from?.pathname || redirectTo;
    return <Navigate to={redirectTarget} replace />;
  }
  return <>{children}</>;
};

export default RedirectIfAuthenticated;
