import React, { useState, useEffect, useRef } from 'react';
import { Input, Button } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

interface EmailCodeInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  onSendCode?: () => Promise<void> | void;
  disabled?: boolean;
  countdownTime?: number; // 倒计时时间，默认60秒
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

const EmailCodeInput: React.FC<EmailCodeInputProps> = ({
  value,
  onChange,
  placeholder,
  onSendCode,
  disabled = false,
  countdownTime = 60,
  className,
  size = 'large',
}) => {
  const [countdown, setCountdown] = useState(0);
  const [isSending, setIsSending] = useState(false);
  const { t } = useLanguage();
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    onChange?.(inputValue);
  };

  const handleSendCode = async () => {
    if (countdown > 0 || isSending) return;

    try {
      setIsSending(true);
      
      // 调用发送验证码的回调
      if (onSendCode) {
        await onSendCode();
      }

      // 开始倒计时
      setCountdown(countdownTime);
      timerRef.current = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error('发送验证码失败:', error);
    } finally {
      setIsSending(false);
    }
  };

  const isButtonDisabled = countdown > 0 || isSending || disabled;

  return (
    <Input
      value={value}
      onChange={handleInputChange}
      placeholder={placeholder || t('auth.login.form.emailCodeLabel')}
      size={size}
      disabled={disabled}
      className={
        className ||
        'placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)'
      }
      suffix={
        <div className="flex items-center">
          {countdown > 0 && (
            <span className="mr-2 text-[12px] text-gray-500">
              {countdown}s
            </span>
          )}
          <Button
            variant="link"
            onClick={handleSendCode}
            disabled={isButtonDisabled}
            loading={isSending}
            className="text-[12px] text-[#ff5e13] hover:underline disabled:text-gray-400"
          >
            {countdown > 0
              ? t('auth.login.form.resend')
              : isSending
              ? '发送中...'
              : t('auth.login.form.resend')}
          </Button>
        </div>
      }
    />
  );
};

export default EmailCodeInput;
