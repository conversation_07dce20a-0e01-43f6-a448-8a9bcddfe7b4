import React from 'react';
import { Select } from 'antd';
import { useAppStore } from '@/store';
import { GlobalOutlined } from '@ant-design/icons';
import { SUPPORTED_LANGUAGES } from '../locales';
import type { Language } from '@/store/index';

const SelectLanguage: React.FC<{ className?: string }> = ({ className }) => {
  const { language, setLanguage } = useAppStore();

  const handleLanguageChange = (value: Language) => {
    setLanguage(value);
  };
  return (
    <Select
      value={language}
      onChange={handleLanguageChange}
      size="middle"
      className={`min-w-[120px] ${className}`}
      suffixIcon={<GlobalOutlined />}
      options={SUPPORTED_LANGUAGES.map(lang => ({
        value: lang.code,
        label: (
          <div className="flex items-center">
            <span>{lang.name}</span>
          </div>
        ),
      }))}
    />
  );
};

export default SelectLanguage;
