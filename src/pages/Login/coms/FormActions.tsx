import { Link } from 'react-router-dom';
import i18n from '@/locales';

interface FormActionsProps {
  onSubmit: () => void;
  buttonText?: string;
}

const FormActions: React.FC<FormActionsProps> = ({
  onSubmit,
  buttonText = i18n.t('auth.register.step1.buttons.next'),
}) => {
  const t = i18n.t;
  return (
    <>
      {/* 登录提示文字 */}
      <div className="mb-64px">
        <span className="font-inter text-[12px] text-[#656565] font-medium">
          {t('auth.login.form.noAccount')}
        </span>

        <Link
          to="/register"
          className="font-inter ml-1 text-[12px] font-medium !text-[#ff5e13] hover:underline"
        >
          {t('auth.login.form.register')}
        </Link>
      </div>

      {/* Next按钮 */}
      <button
        onClick={onSubmit}
        className="font-arial h-[63px] w-full cursor-pointer rounded-md border-none bg-[#ff5e13] text-[16px] text-black font-bold transition-colors hover:bg-[#e5541a]"
      >
        {buttonText}
      </button>
    </>
  );
};
export default FormActions;
