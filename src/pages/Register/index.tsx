import React from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { UserRole } from '@/types';

// 注册表单数据类型
interface RegisterFormData {
  email?: string;
  alias?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  address?: string;
  country?: string;
  state?: string;
  postalCode?: string;
  password?: string;
  role?: UserRole;
}

// 创建Context来管理注册状态
export const RegisterContext = React.createContext<{
  formData: RegisterFormData;
  updateFormData: (data: Partial<RegisterFormData>) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  clearFormData: () => void;
}>({
  formData: {},
  updateFormData: () => {},
  currentStep: 1,
  setCurrentStep: () => {},
  clearFormData: () => {},
});

const Register: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [formData, setFormData] = React.useState<RegisterFormData>({});
  const [currentStep, setCurrentStep] = React.useState(1);

  // 更新表单数据
  const updateFormData = React.useCallback(
    (data: Partial<RegisterFormData>) => {
      setFormData(prev => ({ ...prev, ...data }));
    },
    []
  );

  // 清空表单数据
  const clearFormData = React.useCallback(() => {
    setFormData({});
    setCurrentStep(1);
  }, []);

  // 根据当前路径设置步骤
  React.useEffect(() => {
    const path = location.pathname;
    if (path.includes('/email')) {
      setCurrentStep(1);
    } else if (path.includes('/alias')) {
      setCurrentStep(2);
    } else if (path.includes('/personal-info')) {
      setCurrentStep(3);
    } else if (path.includes('/password')) {
      setCurrentStep(4);
    }
  }, [location.pathname]);

  // 初始化时重定向到第一步
  React.useEffect(() => {
    if (location.pathname === '/register') {
      navigate('/register/email', { replace: true });
    }
  }, [location.pathname, navigate]);

  const contextValue = React.useMemo(
    () => ({
      formData,
      updateFormData,
      currentStep,
      setCurrentStep,
      clearFormData,
    }),
    [formData, updateFormData, currentStep, clearFormData]
  );

  return (
    <RegisterContext.Provider value={contextValue}>
      <Outlet />
    </RegisterContext.Provider>
  );
};

export default Register;

// 导出hook方便子组件使用
export const useRegisterContext = () => {
  const context = React.useContext(RegisterContext);
  if (!context) {
    throw new Error('useRegisterContext must be used within RegisterContext');
  }
  return context;
};
