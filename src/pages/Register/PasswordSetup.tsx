import React from 'react';
import { Card, Form, Input, Button, Typography, message, Checkbox } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterContext } from './index';
import { useAuthStore } from '@/store';
import { ApiService } from '@/services';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import PasswordRequirements from '@/components/PasswordRequirements';

const { Title, Text } = Typography;

const PasswordSetup: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData, setCurrentStep, clearFormData } =
    useRegisterContext();
  const { login } = useAuthStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const [password, setPassword] = React.useState('');
  const [agreeTerms, setAgreeTerms] = React.useState(false);

  const onPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);
  };

  const onFinish = async (values: { password: string; confirm: string }) => {
    if (!agreeTerms) {
      message.error(t('auth.register.step4.form.agreeTermsRequired'));
      return;
    }

    const finalData = {
      ...formData,
      password: values.password,
    };

    setLoading(true);
    try {
      const response = await ApiService.post('/auth/register', finalData);
      if (response.data.token) {
        // 注册成功后自动登录
        const permissions = [];
        login(response.data.user, response.data.token, permissions);
        message.success(t('auth.register.success'));
        clearFormData(); // 清空注册数据
        navigate('/');
      } else {
        message.error(response.message || t('auth.register.error'));
      }
    } catch (error: any) {
      message.error(error?.response?.data?.message || t('auth.register.error'));
    } finally {
      setLoading(false);
    }
  };

  const onPrevious = () => {
    setCurrentStep(3);
    navigate('/register/personal-info');
  };

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning(t('auth.register.step4.messages.aliasRequired'));
      navigate('/register/alias');
    } else if (!formData.firstName || !formData.lastName) {
      message.warning(t('auth.register.step4.messages.personalInfoRequired'));
      navigate('/register/personal-info');
    }
  }, [formData, navigate, t]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="max-w-md w-full">
        <Card className="border-0 rounded-lg shadow-lg">
          <div className="p-6">
            <div className="mb-8 text-center">
              <Title level={2} className="mb-0 text-gray-800">
                {t('auth.register.step4.title')}
              </Title>
            </div>

            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                label={
                  <span className="text-base text-gray-700 font-medium">
                    {t('auth.register.step4.form.password')}:
                  </span>
                }
                name="password"
                rules={[
                  {
                    required: true,
                    message: t('auth.register.step4.form.passwordRequired'),
                  },
                  {
                    min: 8,
                    message: t(
                      'auth.register.step4.form.passwordRequirements.length'
                    ),
                  },
                ]}
                className="mb-4"
              >
                <Input.Password
                  placeholder={t(
                    'auth.register.step4.form.passwordPlaceholder'
                  )}
                  onChange={onPasswordChange}
                  iconRender={visible =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                  className="h-12 border-gray-300 rounded-md"
                />
              </Form.Item>

              {/* 密码要求列表 */}
              <PasswordRequirements password={password} />

              <Form.Item
                name="confirm"
                label={
                  <span className="text-base text-gray-700 font-medium">
                    {t('auth.register.step4.form.confirmPassword')}:
                  </span>
                }
                dependencies={['password']}
                rules={[
                  {
                    required: true,
                    message: t(
                      'auth.register.step4.form.confirmPasswordRequired'
                    ),
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error(
                          t('auth.register.step4.form.passwordMismatch')
                        )
                      );
                    },
                  }),
                ]}
                className="mb-6"
              >
                <Input.Password
                  placeholder={t(
                    'auth.register.step4.form.confirmPasswordPlaceholder'
                  )}
                  iconRender={visible =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                  className="h-12 border-gray-300 rounded-md"
                />
              </Form.Item>

              {/* 服务条款复选框 */}
              <div className="mb-6">
                <Checkbox
                  checked={agreeTerms}
                  onChange={e => setAgreeTerms(e.target.checked)}
                  className="text-sm"
                >
                  {t('auth.register.step4.form.agreeTerms')}{' '}
                  <a href="#" className="text-blue-600 hover:text-blue-700">
                    {t('auth.register.step4.form.termsOfService')}
                  </a>{' '}
                  {t('auth.register.step4.form.and')}{' '}
                  <a href="#" className="text-blue-600 hover:text-blue-700">
                    {t('auth.register.step4.form.privacyPolicy')}
                  </a>
                </Checkbox>
              </div>

              <Button
                type="primary"
                htmlType="submit"
                size="large"
                className="h-12 w-full border-green-500 rounded-lg bg-green-500 text-base text-white font-medium hover:border-green-600 hover:bg-green-600"
                loading={loading}
                disabled={!agreeTerms}
              >
                {t('auth.register.step4.form.signUp')}
              </Button>
            </Form>

            <div className="mt-6 flex justify-center">
              <Button
                onClick={onPrevious}
                type="text"
                className="text-gray-600 hover:text-gray-800"
                disabled={loading}
              >
                {t('auth.register.step4.buttons.prev')}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PasswordSetup;
