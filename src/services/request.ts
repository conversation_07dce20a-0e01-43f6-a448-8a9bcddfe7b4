import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useAppStore } from '../store';
import { isDevelopment } from '@/utils/utils';
import i18n from '@/locales';
import { message } from 'antd';

// 扩展AxiosRequestConfig以支持loading配置
declare module 'axios' {
  interface AxiosRequestConfig {
    /** 是否显示全局loading，默认为true */
    showLoading?: boolean;
    /** 自定义loading文本 */
    loadingText?: string;
    /** 请求元数据 */
    metadata?: {
      requestId: string;
    };
  }
}

// Loading状态类型
export interface LoadingState {
  isLoading: boolean;
  loadingText: string;
  requestCount: number;
}

// 简单的loading管理器
class LoadingManager {
  private requestQueue = new Set<string>();
  private loadingText = i18n.t('common.messages.loading');
  private listeners = new Set<(state: LoadingState) => void>();

  // 订阅loading状态变化
  subscribe(listener: (state: LoadingState) => void) {
    this.listeners.add(listener);

    return () => {
      this.listeners.delete(listener);
    };
  }

  // 通知所有监听者
  private notify() {
    const state: LoadingState = {
      isLoading: this.requestQueue.size > 0,
      loadingText: this.loadingText,
      requestCount: this.requestQueue.size,
    };

    this.listeners.forEach(listener => listener(state));
  }

  addRequest(requestId: string, showLoading = true) {
    if (showLoading) {
      this.requestQueue.add(requestId);
      this.notify();
      // console.log(
      //   `[Loading] 添加请求: ${requestId}, 队列长度: ${this.requestQueue.size}`
      // );
    }
  }

  removeRequest(requestId: string) {
    this.requestQueue.delete(requestId);
    this.notify();
    // console.log(
    //   `[Loading] 移除请求: ${requestId}, 队列长度: ${this.requestQueue.size}`
    // );
  }

  setLoadingText(text: string) {
    this.loadingText = text;
    if (this.requestQueue.size > 0) {
      this.notify();
    }
  }

  // 手动控制loading状态
  showLoading(text?: string) {
    if (text) this.setLoadingText(text);
    this.addRequest('manual-loading');
  }

  hideLoading() {
    this.removeRequest('manual-loading');
  }

  // 获取当前状态
  getState(): LoadingState {
    return {
      isLoading: this.requestQueue.size > 0,
      loadingText: this.loadingText,
      requestCount: this.requestQueue.size,
    };
  }
}

// 创建loading管理器实例
export const loadingManager = new LoadingManager();

// 生成唯一请求ID
const generateRequestId = () =>
  `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// API 基础配置
//  开发模式会在请求时传入完整的URL，staging和prod模式使用全局配置的BASE_URL
const BASE_URL = isDevelopment ? '' : import.meta.env.VITE_API_BASE_URL;

// 获取完整URL
export function getUrl(type: 'mock' | 'dev', url) {
  switch (type) {
    case 'mock':
      return 'http://localhost:3001/mock' + url;
    case 'dev':
      return import.meta.env.VITE_API_BASE_URL + url;
    default:
      return '' + url;
  }
}
// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 生成唯一的请求ID
    const requestId = generateRequestId();
    config.metadata = { requestId };

    // 添加认证 token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 处理loading状态
    const showLoading = config.showLoading !== false; // 默认显示loading

    // 设置自定义loading文本
    if (config.loadingText) {
      loadingManager.setLoadingText(config.loadingText);
    }

    // 添加请求到队列
    loadingManager.addRequest(requestId, showLoading);

    return config;
  },
  error => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 从loading队列中移除请求
    const requestId = response.config.metadata?.requestId;
    if (requestId) {
      loadingManager.removeRequest(requestId);
    }

    return response;
  },
  error => {
    // 从loading队列中移除请求
    const requestId = error.config?.metadata?.requestId;
    if (requestId) {
      loadingManager.removeRequest(requestId);
    }

    console.error('Response Error:', {
      message: error.message,
      status: error.response?.status,
      url: error.config?.url,
    });

    // 处理常见错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          message.error(i18n.t('common.messages.loginExpired'));
          // 未授权，清除 token 并跳转登录
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          message.error(i18n.t('common.messages.forbidden'));
          break;
        case 404:
          message.error(i18n.t('common.messages.notFound'));
          break;
        case 500:
          message.error(i18n.t('common.messages.serverError'));
          break;
        default:
          message.error(i18n.t('common.messages.unknownError'));
      }
    } else if (error.request) {
      message.error(i18n.t('common.messages.networkError'));
    }

    return Promise.reject(error);
  }
);

// API 接口类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  body: T;
}

// 封装的 API 方法
export class ApiService {
  static async get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.get(url, config);
    return response.data;
  }

  static async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.post(url, data, config);
    return response.data;
  }

  static async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.put(url, data, config);
    return response.data;
  }

  static async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.delete(url, config);
    return response.data;
  }

  static async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.patch(url, data, config);
    return response.data;
  }

  // 导出loading控制方法供外部使用
  static showLoading = (text?: string) => loadingManager.showLoading(text);
  static hideLoading = () => loadingManager.hideLoading();
}

export default apiClient;
